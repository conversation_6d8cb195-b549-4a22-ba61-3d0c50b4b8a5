<template>
    <div class="all-padding-16 width-100 model-info-box height-100" v-if="isLoading">
        <el-tabs v-model="activeCategory" stretch :tab-position="'left'" class="model-tabs">
            <el-tab-pane class="height-100" :label="category.title" :name="category.name"
                         v-for="category in categoryDetailList" :key="category.name">

                <div v-if="activeCategory === category.name" class="height-100 model-content">
                    <!-- 固定的标签栏 -->
                    <div class="model-box-fixed display-flex gap-16 flex-wrap model-box tb-padding-10">
                        <div class="model-label text-nowrap  border all-padding-8 border-radius-4 font-12  display-flex top-bottom-center left-right-center"
                                v-for="item in category.children" :key="item.name" @click="jumpHref(item.name)"
                                :class="item.showType === 'list' ? (item.total === 0 ? 'not-allow-back not-allow' : 'pointer back-color-white ') : 'pointer back-color-white '">
                            {{ item.title }} <span class="l-margin-5" v-show="item.total && item.total > 0">{{
                                item.total }}</span>
                        </div>
                    </div>
                    <!-- 可滚动的内容区域 -->
                    <div class="model-content-scrollable">
                        <div v-for="item in category.children" :key="item.name" :id="'model-' + item.name"
                             class="model-item t-margin-21">
                            <div class="model-title b-margin-17">{{ item.title }}</div>
                            <div v-if="item.showType === 'table'">
                                <SearchCompanyFmForm @updateBuyStatus="updateBuyStatus" :modelItem="item"
                                                     :companyRelateItem="relationModel" />
                            </div>
                            <div v-if="item.showType === 'list'">
                                <SearchCompanyFmTable @updateBuyStatus="updateBuyStatus" :modelItem="item"
                                                      :companyRelateItem="relationModel" @updateTotal="
                                                          (total) => {
                                                              updateModelTotal(total, item)
                                                          }
                                                      " />
                            </div>
                            <div v-if="item.showType === 'merge'">
                                <SearchCompanyFmMerge @updateBuyStatus="updateBuyStatus" :modelItem="item" />
                            </div>
                        </div>
                    </div>
                </div>

            </el-tab-pane>
        </el-tabs>
    </div>
    <div v-else>
        <el-skeleton :rows="5" animated />
    </div>
</template>

<script lang='ts' setup>
import { ref, onMounted, defineExpose, onUnmounted } from 'vue'
import type { Ref } from 'vue'
import type { IGetModelCategoryResponse } from '@/types/company'

import aicService from '@/service/aicService'
import eventBus from '@/utils/eventBus'
import SearchCompanyFmForm from '@/components/form-making/SearchCompanyFmGenerateForm.vue'
import SearchCompanyFmTable from '@/components/form-making/SearchCompanyFmGenerateTable.vue'
import SearchCompanyFmMerge from '@/components/form-making/SearchCompanyFmGenerateMerge.vue'

const isLoading: Ref<boolean> = ref(false)

const activeCategory: Ref<string> = ref('')

const categoryDetailList: Ref<IGetModelCategoryResponse[]> = ref([])

const updateModelTotal = (total: number, item: IGetModelCategoryResponse) => {
    item.total = total
}
const jumpHref = (name: string) => {
    // 找到可滚动的容器
    const scrollContainer = document.querySelector('.model-content-scrollable')
    const targetElement = document.getElementById(`model-${name}`)

    if (scrollContainer && targetElement) {
        // 计算目标元素相对于滚动容器的位置
        const containerRect = scrollContainer.getBoundingClientRect()
        const targetRect = targetElement.getBoundingClientRect()
        const scrollTop = scrollContainer.scrollTop

        // 计算需要滚动的距离
        const targetScrollTop = scrollTop + (targetRect.top - containerRect.top) - 20 // 20px 的偏移量

        // 平滑滚动到目标位置
        scrollContainer.scrollTo({
            top: targetScrollTop,
            behavior: 'smooth'
        })
    }
}

const relationModel: Ref<IGetModelCategoryResponse> = ref({} as IGetModelCategoryResponse)
const getRelateModel = () => {
    aicService.gsGetGetAppointDetailModel({ name: 'CompanyRelation' }).then(res => {
        relationModel.value = res.data
    })
}

const getModelList = () => {
    isLoading.value = false
    aicService.conditionGetDetailModel().then((res: IGetModelCategoryResponse[]) => {
        categoryDetailList.value = res
        if (!activeCategory.value) {
            activeCategory.value = res[0].name
        }
        isLoading.value = true
    })
}

const updateBuyStatus = () => {
    getModelList()
    eventBus.$emit('refreshBuyStatus')
}

// 移除了 modelHeight 计算，现在使用 CSS flex 布局自动处理高度

onUnmounted(() => {
    eventBus.$off('refreshBuyStatus', () => { })
})

onMounted(() => {
    getRelateModel()
    getModelList()
    eventBus.$on('refreshBuyStatus', () => {
        getModelList()
    })
})
defineExpose({
    getModelList
})
</script>

<style lang='scss' scoped>
.model-label {
    width: 135px;
}

.model-item {
    .model-title {
        border-left: 4px solid var(--main-blue-);
        padding-left: 8px;
    }
}

.model-info-box {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.model-tabs {
    height: 100%;
    display: flex;

    :deep(.el-tabs__content) {
        height: 100%;
        flex: 1;
        overflow: hidden;
    }

    :deep(.el-tab-pane) {
        height: 100%;
    }
}

.model-content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.model-box-fixed {
    position: sticky;
    top: 0;
    z-index: 10;
    background: white;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 16px;
}

.model-box {
    background: linear-gradient(to right,
            rgba(235, 241, 255, 1) 0%,
            rgba(255, 255, 255, 1) 70.15%,
            rgba(255, 255, 255, 1) 100%);
}

.model-content-scrollable {
    flex: 1;
    overflow-y: auto;
    padding: 0 16px;

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
        width: 6px;
    }

    &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
            background: #a8a8a8;
        }
    }
}

.not-allow-back {
    background-color: var(--second-blue);
}
</style>